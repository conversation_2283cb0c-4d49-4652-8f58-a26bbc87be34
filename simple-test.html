<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>集成测试</title>
</head>
<body>
    <h1>集成测试</h1>
    
    <script src="http://**************:8094/client_app_iframe/index.js?linkType=2"></script>
    
    <script>

        window.onload = function() {
            var params = {
                "deptId": "0001",
                "doctorId": "0002",
                "appKey": "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",
                "appSecretKey": "YYCloud1644286723584",
                "qualityTarget": "2"
            }
            //设置当前科室和医生
            _clientAppSDK.changeParams(params)
            var history = {
                "patientId": "88888888",
                "visitSn": "88888888",
                "dataPacket": [
                    {
                        "tableCode": "b02_1",
                        "data": [
                    {
                        "patient_id": "88888888",
                        "visit_sn": "88888888",
                        "visit_type": "住院" ,
                        "hospital_code":"25ab66fb45de3339c17a53",
                        "hospital_name": "山西省肿瘤医院",
                        "visit_card_no": "ttt1234",
                        "outpatient_no": "",
                        "visit_times": "",
                        "visit_datetime": "",
                        "medical_record_no": "",
                        "inpatient_no": "102310",
                        "hospitalization_times": "1",
                        "admission_datetime": "2022-04-16 07:11:07",
                        "discharge_datetime": "2022-04-16 07:11:07",
                        "visit_doctor_no": "001346",
                        "visit_doctor_name": "张三",
                        "name": "李四",
                        "gender": "男",
                        "patient_gender": "NULL",
                        "date_of_birth": "1987-05-25",
                        "occupation_code": "",
                        "occupation_name": "程序员",
                        "nationality": "中国",
                        "ethnicity": "汉族",
                        "education": "小学毕业",
                        "education_code": "12",
                        "marital_status": "已婚",
                        "marital_status_code": "",
                        "newbron_mark": "否",
                        "visit_status": "是",
                        "patient_identity": "其他",
                        "blood_type_s": "NULL",
                        "bolld_type_e": "NULL",
                        "height": "NULL",
                        "weight": "NULL",
                        "certificate_type": "身份证",
                        "certificate_no": "xxxxxxxxxxxxxxxxxx",
                        "idcard_no": "xxxxxxxxxxxxxxxxxx",
                        "health_card_type": "NULL",
                        "health_card_no": "NULL",
                        "insurance_type": "云南省属地州职工医保",
                        "insurance_no": "HZ2501085623",
                        "domicile_province": "",
                        "domicile_city": "",
                        "domicile_county": "",
                        "domicile_address": "",
                        "home_address": "",
                        "phone_no": "18877887778",
                        "phone_no2": "",
                        "email": "",
                        "weixin": "",
                        "contact_person1": "",
                        "contact_phone_no1": "",
                        "contact_person2": "",
                        "contact_phone_no2": "",
                        "abo_blood_type": "",
                        "rh_blood_type": "",
                        "tsblbs": "NULL",
                        "is_hospital_infected": "NULL",
                        "extend_data1": "NULL",
                        "extend_data2": "NULL",
                        "record_status": "1",
                        "record_datetime": "",
                        "record_update_datetime": "",
                        "admission_dept_code": "034",
                        "admission_dept_name": "肿瘤内科",
                        "admission_ward_code": "123",
                        "admission_ward_name": "化疗病房",
                        "admission_bed_code": "",
                        "admission_bed_name": "28床",
                        "current_dept_code": "034",
                        "current_dept_name": "肿瘤内科",
                        "current_ward_code": "123",
                        "current_ward_name": "化疗病房",
                        "current_bed_code": "",
                        "current_bed_name": "28床",
                        "admission_medical_team_code": "sn1",
                        "admission_medical_team_name": "神经内诊疗一组",
                        "chief_physician_id": "456",
                        "chief_physician": "王五",
                        "attending_physician_id": "001346",
                        "attending_physician": "张文宏",
                        "responsible_nurse_id": "",
                        "responsible_nurse": "",
                        "dmission_type_code": "w01",
                        "admission_type_name": "急诊"
                    }
                ]
            }
        ]
        }
            //请求历史数据，住院患者
            _clientAppSDK.history(history)
        }
    </script>
</body>
</html>
