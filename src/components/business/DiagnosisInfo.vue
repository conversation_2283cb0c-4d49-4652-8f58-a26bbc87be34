<template>
  <div class="diagnosis-info-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧诊断信息内容区域 -->
    <div class="diagnosis-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载诊断信息...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-wrapper">
        <!-- 页面标题 -->
        <div class="page-title">
          <h2 class="title-text">诊断信息</h2>
        </div>

        <!-- 诊断信息表格 -->
        <div class="diagnosis-table-container">
          <table class="diagnosis-table">
            <!-- 表头 -->
            <thead>
              <tr class="table-header">
                <th class="header-cell">
                  <span class="required-field">*</span>诊断名称
                </th>
                <th class="header-cell">诊断编码</th>
                <th class="header-cell">
                  <span class="required-field">*</span>诊断类型
                </th>
                <th class="header-cell">
                  <span class="required-field">*</span>是否主要诊断
                </th>
                <th class="header-cell">诊断说明</th>
                <th class="header-cell">操作</th>
              </tr>
            </thead>
            <!-- 表体 -->
            <tbody>
              <!-- 数据行 -->
              <tr 
                v-for="(item, index) in diagnosisList" 
                :key="item.diagId || index"
                class="table-row"
              >
                <!-- 诊断名称 -->
                <td class="table-cell">
                  <el-select
                    v-if="editingIndex === index"
                    v-model="editForm.diagName"
                    placeholder="请选择诊断名称"
                    class="cell-input"
                    filterable
                    remote
                    :remote-method="searchDiagnosisNames"
                    :loading="diagnosisNameLoading"
                    @change="handleDiagnosisNameChange"
                    @focus="handleDiagnosisNameFocus"
                    v-load-more="loadMoreDiagnosisNames"
                  >
                    <el-option
                      v-for="option in diagnosisNameOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.label"
                    />
                    <div v-if="diagnosisNamePagination.loading" class="load-more-indicator">
                      正在加载...
                    </div>
                  </el-select>
                  <span v-else>{{ getDiagnosisNameLabel(item.diagCode) || item.diagName }}</span>
                </td>
                <!-- 诊断编码 -->
                <td class="table-cell">
                  <span>{{ item.diagCode }}</span>
                </td>
                <!-- 诊断类型 -->
                <td class="table-cell">
                  <el-select
                    v-if="editingIndex === index"
                    v-model="editForm.diagType"
                    placeholder="请选择诊断类型"
                    class="cell-input"
                    :loading="diagnosisTypeLoading"
                  >
                    <el-option
                      v-for="option in diagnosisTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <span v-else>{{ getDiagnosisTypeLabel(item.diagType) }}</span>
                </td>
                <!-- 是否主要诊断 -->
                <td class="table-cell">
                  <el-select
                    v-if="editingIndex === index"
                    v-model="editForm.maindiagMark"
                    placeholder="请选择"
                    class="cell-input"
                    :loading="yesOrNoLoading"
                  >
                    <el-option
                      v-for="option in yesOrNoOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <span v-else>{{ getYesOrNoLabel(item.maindiagMark) }}</span>
                </td>
                <!-- 诊断说明 -->
                <td class="table-cell">
                  <el-input
                    v-if="editingIndex === index"
                    v-model="editForm.diagExplanation"
                    placeholder="请输入诊断说明"
                    class="cell-input"
                  />
                  <span v-else>{{ item.diagExplanation }}</span>
                </td>
                <!-- 操作 -->
                <td class="table-cell">
                  <div v-if="editingIndex === index" class="action-buttons">
                    <el-button 
                      type="primary" 
                      size="small" 
                      @click="handleSave"
                      :loading="saveLoading"
                    >
                      保存
                    </el-button>
                    <el-button 
                      size="small" 
                      @click="handleCancelEdit"
                    >
                      取消
                    </el-button>
                  </div>
                  <div v-else class="action-buttons">
                    <el-button 
                      type="primary" 
                      size="small" 
                      @click="handleEdit(index)"
                    >
                      编辑
                    </el-button>
                    <el-button 
                      type="danger" 
                      size="small" 
                      @click="handleDelete(item)"
                      :loading="deleteLoading"
                    >
                      删除
                    </el-button>
                  </div>
                </td>
              </tr>
              <!-- 编辑添加行 -->
              <tr v-if="isAdding" class="table-row add-row">
                <td class="table-cell">
                  <el-select
                    v-model="addForm.diagName"
                    placeholder="请选择诊断名称"
                    class="cell-input"
                    filterable
                    remote
                    :remote-method="searchDiagnosisNames"
                    :loading="diagnosisNameLoading"
                    @change="handleAddDiagnosisNameChange"
                    @focus="handleDiagnosisNameFocus"
                    v-load-more="loadMoreDiagnosisNames"
                  >
                    <el-option
                      v-for="option in diagnosisNameOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.label"
                    />
                    <div v-if="diagnosisNamePagination.loading" class="load-more-indicator">
                      正在加载...
                    </div>
                  </el-select>
                </td>
                <td class="table-cell">
                  <span>{{ addForm.diagCode }}</span>
                </td>
                <td class="table-cell">
                  <el-select
                    v-model="addForm.diagType"
                    placeholder="请选择诊断类型"
                    class="cell-input"
                    :loading="diagnosisTypeLoading"
                  >
                    <el-option
                      v-for="option in diagnosisTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </td>
                <td class="table-cell">
                  <el-select
                    v-model="addForm.maindiagMark"
                    placeholder="请选择"
                    class="cell-input"
                    :loading="yesOrNoLoading"
                  >
                    <el-option
                      v-for="option in yesOrNoOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </td>
                <td class="table-cell">
                  <el-input
                    v-model="addForm.diagExplanation"
                    placeholder="请输入诊断说明"
                    class="cell-input"
                  />
                </td>
                <td class="table-cell">
                  <div class="action-buttons">
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleSaveAdd"
                      :loading="saveLoading"
                    >
                      保存
                    </el-button>
                    <el-button
                      size="small"
                      @click="handleCancelAdd"
                    >
                      取消
                    </el-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 添加按钮 - 使用div居中 -->
          <div v-if="!isAdding" class="add-button-container">
            <el-button
              type="primary"
              @click="handleAdd"
              class="add-record-btn"
              :icon="Plus"
            >
              添加
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { apiServices } from '@/api'
import { useDictionaryStore } from '@/stores/dictionaryStore'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 自定义指令：v-load-more
const vLoadMore = {
  mounted(el, binding) {
    const observer = new MutationObserver(() => {
      const dropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"]) .el-select-dropdown__wrap')
      if (dropdown && !dropdown.hasAttribute('data-scroll-listener')) {
        dropdown.setAttribute('data-scroll-listener', 'true')
        dropdown.addEventListener('scroll', function() {
          const isBottom = this.scrollHeight - this.scrollTop <= this.clientHeight + 1
          if (isBottom) {
            binding.value()
          }
        })
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    el.vLoadMoreObserver = observer
  },
  unmounted(el) {
    if (el.vLoadMoreObserver) {
      el.vLoadMoreObserver.disconnect()
    }
  }
}

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['count-updated'])

// 使用字典Store
const dictionaryStore = useDictionaryStore()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// 字典数据相关状态
const diagnosisNameOptions = ref([])
const diagnosisTypeOptions = ref([])
const yesOrNoOptions = ref([])
const diagnosisNameLoading = ref(false)
const diagnosisTypeLoading = ref(false)
const yesOrNoLoading = ref(false)

// 诊断名称分页加载状态
const diagnosisNamePagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  loading: false,
  keyword: ''
})

// 诊断信息列表
const diagnosisList = ref([])

// 编辑状态
const editingIndex = ref(-1)
const isAdding = ref(false)

// 编辑表单
const editForm = reactive({
  diagId: '',
  diagSource: '',
  diagName: '',
  diagCode: '',
  diagType: '',
  maindiagMark: '',
  diagExplanation: '',
  diagDatetime: ''
})

// 添加表单
const addForm = reactive({
  diagName: '',
  diagCode: '',
  diagType: '',
  maindiagMark: '',
  diagExplanation: ''
})

// ========== 字典数据加载方法 ==========

/**
 * 加载字典数据
 */
const loadDictionaries = async () => {
  try {
    // 并行加载字典数据
    await Promise.all([
      loadDiagnosisNames(),
      loadDiagnosisTypes(),
      loadYesOrNo()
    ])
  } catch (error) {
    ElMessage.error('字典数据加载失败，请刷新页面重试')
  }
}

/**
 * 加载诊断名称字典
 */
const loadDiagnosisNames = async (isLoadMore = false) => {
  if (diagnosisNamePagination.loading) return

  try {
    diagnosisNamePagination.loading = true

    const params = {
      pageNum: diagnosisNamePagination.pageNum,
      pageSize: diagnosisNamePagination.pageSize,
      label: diagnosisNamePagination.keyword
    }

    const data = await apiServices.dictionary.getDiagnosisNamesByPage(params)

    const newOptions = data.records.map(item => ({
      label: item.label,
      value: item.value
    }))

    if (isLoadMore) {
      diagnosisNameOptions.value.push(...newOptions)
    } else {
      diagnosisNameOptions.value = newOptions
    }

    diagnosisNamePagination.total = data.total
  } catch (error) {
    if (!isLoadMore) {
      diagnosisNameOptions.value = []
    }
  } finally {
    diagnosisNamePagination.loading = false
  }
}

/**
 * 加载更多诊断名称
 */
const loadMoreDiagnosisNames = () => {
  const { pageNum, pageSize, total } = diagnosisNamePagination
  if ((pageNum * pageSize < total) && !diagnosisNamePagination.loading) {
    diagnosisNamePagination.pageNum++
    loadDiagnosisNames(true)
  }
}

/**
 * 搜索诊断名称
 */
const searchDiagnosisNames = async (query) => {
  diagnosisNamePagination.keyword = query
  diagnosisNamePagination.pageNum = 1
  diagnosisNameLoading.value = true
  try {
    await loadDiagnosisNames()
  } catch (error) {
    ElMessage.error('搜索诊断名称失败')
  } finally {
    diagnosisNameLoading.value = false
  }
}

/**
 * 诊断名称选择框聚焦时触发
 */
const handleDiagnosisNameFocus = () => {
  if (diagnosisNameOptions.value.length === 0) {
    searchDiagnosisNames('')
  }
}

/**
 * 加载诊断类型字典
 */
const loadDiagnosisTypes = async () => {
  try {
    diagnosisTypeLoading.value = true
    diagnosisTypeOptions.value = await dictionaryStore.getDiagnosisTypes()
  } catch (error) {
    diagnosisTypeOptions.value = []
  } finally {
    diagnosisTypeLoading.value = false
  }
}

/**
 * 加载是否主要诊断字典
 */
const loadYesOrNo = async () => {
  try {
    yesOrNoLoading.value = true
    yesOrNoOptions.value = await dictionaryStore.getYesOrNo()
  } catch (error) {
    yesOrNoOptions.value = []
  } finally {
    yesOrNoLoading.value = false
  }
}

// ========== 数据加载方法 ==========

/**
 * 加载诊断信息列表
 */
const loadDiagnosisList = async () => {
  if (!props.patient?.visitSn) {
    return
  }

  try {
    loading.value = true

    const data = await apiServices.diagnosisInfo.getList(props.patient.visitSn)
    diagnosisList.value = data || []

  } catch (error) {
    ElMessage.error('加载诊断信息列表失败: ' + error.message)
    diagnosisList.value = []
  } finally {
    loading.value = false
  }
}

// ========== 工具方法 ==========

/**
 * 获取诊断类型标签
 */
const getDiagnosisTypeLabel = (value) => {
  const option = diagnosisTypeOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 获取是否主要诊断标签
 */
const getYesOrNoLabel = (value) => {
  const option = yesOrNoOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 获取诊断名称标签
 */
const getDiagnosisNameLabel = (value) => {
  // 由于远程搜索，options可能不完整，直接返回diagName
  const item = diagnosisList.value.find(d => d.diagCode === value)
  return item ? item.diagName : value
}

/**
 * 清空编辑表单
 */
const clearEditForm = () => {
  Object.assign(editForm, {
    diagId: '',
    diagSource: '',
    diagName: '',
    diagCode: '',
    diagType: '',
    maindiagMark: '',
    diagExplanation: '',
    diagDatetime: ''
  })
}

/**
 * 清空添加表单
 */
const clearAddForm = () => {
  Object.assign(addForm, {
    diagName: '',
    diagCode: '',
    diagType: '',
    maindiagMark: '',
    diagExplanation: ''
  })
}

// ========== 事件处理方法 ==========

/**
 * 处理诊断名称选择变化（编辑模式）
 */
const handleDiagnosisNameChange = (value) => {
  const selectedOption = diagnosisNameOptions.value.find(item => item.label === value)
  if (selectedOption) {
    editForm.diagName = selectedOption.label
    editForm.diagCode = selectedOption.value
  }
}

/**
 * 处理诊断名称选择变化（添加模式）
 */
const handleAddDiagnosisNameChange = (value) => {
  const selectedOption = diagnosisNameOptions.value.find(item => item.label === value)
  if (selectedOption) {
    addForm.diagName = selectedOption.label
    addForm.diagCode = selectedOption.value
  }
}

/**
 * 处理编辑
 */
const handleEdit = async (index) => {
  // 如果有正在编辑的行，先保存
  if (editingIndex.value !== -1) {
    await handleSave()
  }

  // 如果有正在添加的行，取消添加
  if (isAdding.value) {
    handleCancelAdd()
  }

  const item = diagnosisList.value[index]
  editingIndex.value = index

  // 填充编辑表单
  Object.assign(editForm, {
    diagId: item.diagId || '',
    diagSource: item.diagSource || '',
    diagName: item.diagName || '',
    diagCode: item.diagCode || '',
    diagType: item.diagType || '',
    maindiagMark: item.maindiagMark || '',
    diagExplanation: item.diagExplanation || '',
    diagDatetime: item.diagDatetime || ''
  })
}

/**
 * 处理取消编辑
 */
const handleCancelEdit = () => {
  editingIndex.value = -1
  clearEditForm()
}

/**
 * 处理保存
 */
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 验证必填字段
  if (!editForm.diagName || !editForm.diagType || !editForm.maindiagMark) {
    ElMessage.error('请填写所有必填字段')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据 - 传递诊断名称和编码
    const saveData = {
      visitSn: props.patient.visitSn,
      diagId: editForm.diagId,
      diagSource: editForm.diagSource,
      diagName: editForm.diagName, // 诊断名称
      diagCode: editForm.diagCode, // 诊断编码
      diagType: getDiagnosisTypeLabel(editForm.diagType), // 传递诊断类型名称而不是字典值
      maindiagMark: editForm.maindiagMark,
      diagExplanation: editForm.diagExplanation || '',
      diagDatetime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }


    await apiServices.diagnosisInfo.update(editForm.diagId, editForm.diagSource, saveData)

    ElMessage.success('保存成功')

    // 重新加载列表
    await loadDiagnosisList()

    // 退出编辑模式
    editingIndex.value = -1
    clearEditForm()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

/**
 * 处理删除
 */
const handleDelete = async (item) => {
  if (!item.diagId || !item.diagSource) {
    ElMessage.error('缺少必要的删除参数')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条诊断信息吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true


    await apiServices.diagnosisInfo.delete(item.diagId, item.diagSource)

    ElMessage.success('删除成功')

    // 重新加载列表
    await loadDiagnosisList()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

/**
 * 处理添加
 */
const handleAdd = () => {
  // 如果有正在编辑的行，先保存
  if (editingIndex.value !== -1) {
    handleSave()
  }

  isAdding.value = true
  clearAddForm()
}

/**
 * 处理取消添加
 */
const handleCancelAdd = () => {
  isAdding.value = false
  clearAddForm()
}

/**
 * 处理保存添加
 */
const handleSaveAdd = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 验证必填字段
  if (!addForm.diagName || !addForm.diagType || !addForm.maindiagMark) {
    ElMessage.error('请填写所有必填字段')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据 - 传递诊断名称和编码
    const saveData = {
      visitSn: props.patient.visitSn,
      diagName: addForm.diagName, // 诊断名称
      diagCode: addForm.diagCode, // 诊断编码
      diagType: getDiagnosisTypeLabel(addForm.diagType), // 传递诊断类型名称而不是字典值
      maindiagMark: addForm.maindiagMark,
      diagExplanation: addForm.diagExplanation || '',
      diagDatetime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }


    await apiServices.diagnosisInfo.save(saveData)

    ElMessage.success('添加成功')

    // 重新加载列表
    await loadDiagnosisList()

    // 退出添加模式
    isAdding.value = false
    clearAddForm()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    ElMessage.error('添加失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadDiagnosisList()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(async () => {
  await loadDictionaries()
  if (props.patient?.visitSn) {
    await loadDiagnosisList()
  }
})
</script>

<style scoped>
/* 整体容器 */
.diagnosis-info-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px);
  gap: 20px;
}

/* 右侧诊断信息内容区域 */
.diagnosis-content-area {
  flex: 1;
  height: 100%;
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 页面标题 */
.page-title {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.title-text {
  width: 113px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0;
}

/* 诊断信息表格容器 */
.diagnosis-table-container {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
}

/* 诊断信息表格 */
.diagnosis-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  min-width: 100%;
}

/* 表头样式 */
.table-header {
  height: 44px;
  background: #F4F5F7;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #EBECF0;
}

.header-cell {
  padding: 12px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
}

.header-cell:nth-child(1) { width: 40%; } /* 诊断名称 */
.header-cell:nth-child(2) { width: 10%; } /* 诊断编码 */
.header-cell:nth-child(3) { width: 10%; } /* 诊断类型 */
.header-cell:nth-child(4) { width: 12%; } /* 是否主要诊断 */
.header-cell:nth-child(5) { width: 20%; } /* 诊断说明 */
.header-cell:nth-child(6) { width: 10%; } /* 操作 */

/* 必填字段标识样式 */
.required-field {
  color: #FF4D4F;
  margin-right: 2px;
  font-weight: 500;
}

/* 表格行样式 */
.table-row {
  height: 44px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #EBECF0;
}

.table-row:hover {
  background: #F4F5F7;
}

.add-row {
  background: #FAFCFF !important;
  width: 100%;
}

.add-row:hover {
  background: #FAFCFF !important;
}

/* 表格单元格样式 */
.table-cell {
  padding: 6px 4px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  vertical-align: middle;
  position: relative;
}

/* 单元格内容不是输入框时的样式 */
.table-cell > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* 输入框和选择框样式 */
.cell-input {
  width: 100%;
  height: 32px;
  border-radius: 0px;
}

.cell-input :deep(.el-input__wrapper),
.cell-input :deep(.el-select__wrapper) {
  height: 32px;
  border-radius: 0px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  box-shadow: none;
}

.cell-input :deep(.el-input__inner) {
  height: 30px;
  border: none;
  border-radius: 0px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  box-shadow: none;
}

.cell-input :deep(.el-input__wrapper:focus),
.cell-input :deep(.el-select__wrapper.is-focused) {
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

.cell-input :deep(.el-input__inner:focus) {
  outline: none !important;
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

.cell-input :deep(.el-select__input:focus) {
  outline: none !important;
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.action-buttons .el-button {
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  min-width: auto;
}

/* 添加按钮容器样式 */
.add-button-container {
  width: 100%;
  padding: 8px 12px;
  background: #FAFCFF;
  border-top: 1px solid #EBECF0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 1px solid #EBECF0;
  border-right: 1px solid #EBECF0;
  border-bottom: 1px solid #EBECF0;
}

/* 添加按钮样式 - 参考日常病程记录 */
.add-record-btn {
  width: 80px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #1678FF;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
}

.add-record-btn:hover {
  color: #1678FF;
  background: transparent;
  border: none;
}

.add-record-btn:focus {
  color: #1678FF;
  outline: none;
  background: transparent;
  border: none;
}

/* 新建模式时的激活状态 */
.add-record-btn.active {
  color: #1678FF;
}

.load-more-indicator {
  padding: 8px 12px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
