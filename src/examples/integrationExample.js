/**
 * 第三方系统集成使用示例
 * 展示如何在实际业务中使用集成功能
 */

import thirdPartyIntegration from '@/services/thirdPartyIntegration'
import { PatientDataAdapter } from '@/utils/patientDataAdapter'
import { getCurrentConfig } from '@/config/thirdPartyConfig'

/**
 * 示例1: 初始化迎春花质控系统
 */
export async function initializeYingchunhuaExample() {
  try {
    // 获取配置
    const config = getCurrentConfig('yingchunhua')
    
    // 补充必要的配置信息
    const fullConfig = {
      ...config,
      sdkUrl: 'http://183.242.68.188:8094/client_app_iframe/index.js',
      appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09', // 已提供
      appSecretKey: 'your_app_secret_key_here', // 待项目经理提供
      deptId: '0001', // 当前科室ID
      doctorId: '0002' // 当前医生ID
    }
    
    // 初始化SDK
    await thirdPartyIntegration.initialize(fullConfig)
    
    console.log('✅ 迎春花质控系统初始化成功')
    return true
  } catch (error) {
    console.error('❌ 初始化失败:', error)
    throw error
  }
}

/**
 * 示例2: 发送住院患者数据
 */
export async function sendInpatientDataExample() {
  // 模拟从API获取的患者数据
  const apiPatientData = {
    patient_id: '00931222',
    name: '张三',
    gender: '男',
    date_of_birth: '1980-05-15',
    phone_no: '13800138000',
    certificate_no: '110101198005150001',
    insurance_type: '职工医保',
    insurance_no: 'HZ2501085623',
    abo_blood_type: 'A型',
    rh_blood_type: '阳性',
    // ... 其他患者信息
  }

  const apiVisitData = {
    visit_sn: '00931222|4623477|1住院',
    visit_type: '住院',
    medical_record_no: '**********',
    inpatient_no: '102310',
    admission_datetime: '2023-04-16 07:11:07',
    discharge_datetime: '2023-04-20 10:30:00',
    visit_doctor_no: '001346',
    visit_doctor_name: '李医生',
    admission_dept_code: '034',
    admission_dept_name: '肿瘤内科',
    current_bed_name: '28床',
    // ... 其他就诊信息
  }

  const hospitalData = {
    code: '25ab66f1a2a2421e9b45de3339c17a53',
    name: '山西省肿瘤医院'
  }

  try {
    // 转换数据格式
    const internalData = PatientDataAdapter.fromApiResponse({
      ...apiPatientData,
      ...apiVisitData,
      hospital_code: hospitalData.code,
      hospital_name: hospitalData.name
    })

    // 转换为第三方系统格式
    const yingchunhuaData = PatientDataAdapter.toYingchunhuaFormat(internalData)

    // 验证数据
    const validation = PatientDataAdapter.validateRequiredFields(
      yingchunhuaData.dataPacket[0].data[0], 
      '住院'
    )

    if (!validation.isValid) {
      console.error('数据验证失败:', validation.errors)
      throw new Error('患者数据不完整: ' + validation.errors.join(', '))
    }

    // 发送数据
    await thirdPartyIntegration.sendPatientData(yingchunhuaData)
    
    console.log('✅ 住院患者数据发送成功')
    return true
  } catch (error) {
    console.error('❌ 发送住院患者数据失败:', error)
    throw error
  }
}

/**
 * 示例3: 发送门诊患者数据
 */
export async function sendOutpatientDataExample() {
  // 模拟门诊患者数据
  const outpatientData = {
    patient: {
      id: 'OP001234',
      name: '李四',
      gender: '女',
      dateOfBirth: '1985-03-20',
      phoneNo: '13900139000',
      certificateNo: '110101198503200002',
      insuranceType: '居民医保',
      insuranceNo: 'JM2501085624'
    },
    visit: {
      visitSn: 'OP001234|20230416001|2门诊',
      visitType: '门诊',
      outpatientNo: '20230416001',
      visitDatetime: '2023-04-16 09:30:00',
      doctorNo: '002001',
      doctorName: '王医生',
      registrationSn: '13423',
      registrationDatetime: '2023-04-16 08:45:00',
      registrationDeptCode: '15',
      registrationDeptName: '肿瘤科',
      registrationChargePrice: 5.0,
      registrationPaidPrice: 5.0,
      isFirstVisit: true
    },
    hospital: {
      code: '25ab66f1a2a2421e9b45de3339c17a53',
      name: '山西省肿瘤医院'
    }
  }

  try {
    // 转换为第三方系统格式
    const yingchunhuaData = PatientDataAdapter.toYingchunhuaFormat(outpatientData)

    // 验证数据
    const validation = PatientDataAdapter.validateRequiredFields(
      yingchunhuaData.dataPacket[0].data[0], 
      '门诊'
    )

    if (!validation.isValid) {
      console.error('数据验证失败:', validation.errors)
      throw new Error('患者数据不完整: ' + validation.errors.join(', '))
    }

    // 发送数据
    await thirdPartyIntegration.sendPatientData(yingchunhuaData)
    
    console.log('✅ 门诊患者数据发送成功')
    return true
  } catch (error) {
    console.error('❌ 发送门诊患者数据失败:', error)
    throw error
  }
}

/**
 * 示例4: 在Vue组件中使用集成功能
 */
export const integrationMixin = {
  data() {
    return {
      integrationStatus: {
        yingchunhua: false,
        drg: false
      }
    }
  },

  async mounted() {
    // 组件挂载时检查集成状态
    await this.checkIntegrationStatus()
  },

  methods: {
    /**
     * 检查集成状态
     */
    async checkIntegrationStatus() {
      try {
        const status = thirdPartyIntegration.getStatus()
        this.integrationStatus.yingchunhua = status.isInitialized
        
        if (!status.isInitialized) {
          console.log('第三方系统未初始化，尝试自动初始化...')
          await this.autoInitializeIntegration()
        }
      } catch (error) {
        console.error('检查集成状态失败:', error)
      }
    },

    /**
     * 自动初始化集成
     */
    async autoInitializeIntegration() {
      try {
        await initializeYingchunhuaExample()
        this.integrationStatus.yingchunhua = true
        this.$message.success('第三方系统初始化成功')
      } catch (error) {
        console.error('自动初始化失败:', error)
        this.$message.error('第三方系统初始化失败，请手动配置')
      }
    },

    /**
     * 发送当前患者数据到第三方系统
     */
    async sendCurrentPatientData(patientData) {
      try {
        if (!this.integrationStatus.yingchunhua) {
          throw new Error('第三方系统未初始化')
        }

        // 根据患者类型选择相应的发送方法
        if (patientData.visit.visitType === '住院') {
          await this.sendInpatientData(patientData)
        } else if (patientData.visit.visitType === '门诊') {
          await this.sendOutpatientData(patientData)
        }

        this.$message.success('患者数据已发送到质控系统')
      } catch (error) {
        console.error('发送患者数据失败:', error)
        this.$message.error('发送失败: ' + error.message)
      }
    },

    /**
     * 发送住院患者数据
     */
    async sendInpatientData(patientData) {
      const yingchunhuaData = PatientDataAdapter.toYingchunhuaFormat(patientData)
      await thirdPartyIntegration.sendPatientData(yingchunhuaData)
    },

    /**
     * 发送门诊患者数据
     */
    async sendOutpatientData(patientData) {
      const yingchunhuaData = PatientDataAdapter.toYingchunhuaFormat(patientData)
      await thirdPartyIntegration.sendPatientData(yingchunhuaData)
    }
  }
}

/**
 * 示例5: 错误处理和重试机制
 */
export class IntegrationErrorHandler {
  static async withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        console.warn(`操作失败，第 ${i + 1} 次重试...`, error.message)
        
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }
    
    throw new Error(`操作失败，已重试 ${maxRetries} 次: ${lastError.message}`)
  }

  static async sendPatientDataWithRetry(patientData) {
    return this.withRetry(async () => {
      const yingchunhuaData = PatientDataAdapter.toYingchunhuaFormat(patientData)
      return await thirdPartyIntegration.sendPatientData(yingchunhuaData)
    })
  }
}

/**
 * 示例6: 批量数据处理
 */
export async function batchSendPatientData(patientList) {
  const results = []
  const batchSize = 10 // 每批处理10个患者
  
  for (let i = 0; i < patientList.length; i += batchSize) {
    const batch = patientList.slice(i, i + batchSize)
    
    const batchPromises = batch.map(async (patient, index) => {
      try {
        await IntegrationErrorHandler.sendPatientDataWithRetry(patient)
        return { index: i + index, success: true, patient: patient.patient.name }
      } catch (error) {
        return { index: i + index, success: false, patient: patient.patient.name, error: error.message }
      }
    })
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
    
    // 批次间延迟，避免过于频繁的请求
    if (i + batchSize < patientList.length) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }
  
  return results
}

export default {
  initializeYingchunhuaExample,
  sendInpatientDataExample,
  sendOutpatientDataExample,
  integrationMixin,
  IntegrationErrorHandler,
  batchSendPatientData
}
