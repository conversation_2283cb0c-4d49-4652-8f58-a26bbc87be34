import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import './styles/index.css'
import App from './App.vue'
import router, { preloadCriticalRoutes } from './router'
import { getDataSource, getDataSourceName, getAllUrlParams } from './utils/urlParams'

// 性能监控
const appStartTime = performance.now()

// 显示数据源信息
const currentDataSource = getDataSource()
const dataSourceName = getDataSourceName(currentDataSource)
const urlParams = getAllUrlParams()

console.group('🏥 医疗系统初始化')
console.log('📊 当前数据源:', `${currentDataSource} (${dataSourceName})`)
console.log('🔗 URL参数:', urlParams)
console.log('🌐 当前URL:', window.location.href)
console.groupEnd()

const app = createApp(App)
const pinia = createPinia()

app.use(ElementPlus, {
  locale: zhCn,
})
app.use(pinia)
app.use(router)

// 应用挂载
app.mount('#app')

// 应用启动性能统计
const appMountTime = performance.now()
const startupTime = appMountTime - appStartTime
console.log(`🚀 应用启动完成，耗时: ${startupTime.toFixed(2)}ms`)

// 预加载关键路由（在应用启动后异步执行）
setTimeout(() => {
  preloadCriticalRoutes()
}, 100)

// 开发环境性能监控
if (import.meta.env.DEV) {
  // 添加性能监控到全局对象，便于调试
  window.__ROUTER_PERFORMANCE__ = {
    getPerformanceData: () => import('./router').then(m => m.getRoutePerformanceData()),
    getAverageTime: () => import('./router').then(m => m.getAverageNavigationTime()),
    getSlowRoutes: (threshold) => import('./router').then(m => m.getSlowRoutes(threshold))
  }

  console.log('🔧 开发模式：可通过 window.__ROUTER_PERFORMANCE__ 查看路由性能数据')
}
