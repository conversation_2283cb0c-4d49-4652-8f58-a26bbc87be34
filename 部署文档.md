# YYHIS Web 部署文档

## 环境要求

- Node.js 20.19.0
- npm 或 yarn

## 快速部署

### 1. 安装依赖
```bash
npm install
```

### 2. 构建项目
```bash
# 开发环境构建
npm run build:dev

# 测试环境构建  
npm run build:test

# 生产环境构建
npm run build:prod
```

### 3. 部署静态文件
构建完成后，将 `dist` 目录下的所有文件部署到 Web 服务器即可。

## Web 服务器配置

### Nginx 配置示例
```nginx
    server {

        listen       9090 ;
        server_name  your-domain.com;

        location / { ## 前端项目
            root   /path/to/dist;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }


        location /api/ { ## 后端项目 - 管理后台
            proxy_pass http://localhost:6596/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }

```


## 环境变量配置

根据部署环境创建对应的环境变量文件：

- `.env.development` - 开发环境
- `.env.test` - 测试环境  
- `.env.production` - 生产环境

主要配置项：
```
# 后端服务器配置
VITE_BACKEND_URL=http://***********:9090

# client端
VITE_YINGCHUNHUA_SDK_URL=http://**************:32103/client_app_iframe/index.js
VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09
VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584
```

